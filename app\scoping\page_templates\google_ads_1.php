<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry" id="google_commercial_1">
    <div class="page-content">

        <h1>Google Ads</h1>

        <?php
        echo $scopingData['text_data']['google_ads_text_1'];
        ?>


        <?php foreach( $adsSearchItems as $adsSearchItem ): ?>
        <?php
        $imageUrl = $adsSearchItem['preview_image']['url'] ?? null;
        if ($imageUrl) {
            // Bilddaten holen
            $imageData = file_get_contents($imageUrl);
            // Als base64 kodieren
            $base64 = base64_encode($imageData);
            // MIME-Type (hier als Beispiel image/jpeg, besser wäre echtes MIME-Type erkennen)
            $mimeType = 'image/jpeg'; // alternativ 'image/png' wenn du es genau weißt
            echo '<img width="'.$adsSearchItem['preview_image']['width'].'" height="'.$adsSearchItem['preview_image']['height'].'" src="data:' . $mimeType . ';base64,' . $base64 . '" />';
            break;
        } else {
            //echo '<p>Kein Bild verfügbar</p>';
        }
        ?>

        <?php endforeach; ?>

    </div>
    <div class="page-footer"><?php echo $pageIndexData ?></div>
</div>
