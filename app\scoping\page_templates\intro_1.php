<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry" id="intro_1">
    <div class="page-content">

        <h1>Einführung und Inhaltsverzeichnis</h1>

        <?php if (!empty($scopingData['text_data']['intro_text_1'])): ?>
            <div class="mb-4">
                <?php echo $scopingData['text_data']['intro_text_1']; ?>
            </div>
        <?php endif; ?>

        <div class="table-of-contents">
            <h2 class="mb-4">
                <i class="fas fa-list-ul text-primary me-2"></i>
                Inhaltsverzeichnis
            </h2>

            <div class="toc-container">
                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-1" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#intro_1">
                        <label  class="toc-label">
                            <i class="fas fa-play-circle text-success me-2"></i>
                            <strong>Einführung und Inhaltsverzeichnis</strong> <span class="p-index">Seite 1</span>
                        </label>
                        </a>
                    </div>
                </div>

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-2" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#local_seo_1">
                        <label  class="toc-label">
                            <i class="fas fa-map-marked-alt text-primary me-2"></i>
                            <strong>Lokales SEO</strong> <span class="p-index">Seite 2</span>
                        </label>
                        </a>
                    </div>
                </div>

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-4" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#seo_1">
                        <label " class="toc-label">
                            <i class="fas fa-search text-warning me-2"></i>
                            <strong>Webseite & SEO</strong><span class="p-index">Seite 4</span>
                        </label>
                        </a>
                    </div>
                </div>

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-5" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#page_speed_1">
                        <label  class="toc-label">
                            <i class="fas fa-tachometer-alt text-danger me-2"></i>
                            <strong>Webseiten Performance</strong><span class="p-index">Seite 5</span>
                        </label>
                        </a>
                    </div>
                </div>

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-6" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#ai_1">
                        <label  class="toc-label">
                            <i class="fas fa-robot text-purple me-2"></i>
                            <strong>KI und die Zukunft</strong><span class="p-index">Seite 7</span>
                        </label>
                        </a>
                    </div>
                </div>

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-7" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#google_commercial_1">
                        <label  class="toc-label">
                            <i class="fab fa-google text-success me-2"></i>
                            <strong>Google Ads</strong><span class="p-index">Seite 8</span>
                        </label>
                        </a>
                    </div>
                </div>

                <!--
                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-8" class="form-check-input">
                    </div>
                    <div class="toc-content">
                        <a href="#social_media_1">
                        <label for="toc-8" class="toc-label">
                            <i class="fas fa-share-alt text-info me-2"></i>
                            <strong>Social Media</strong><span class="p-index">Seite 10</span>
                        </label>
                        </a>
                    </div>
                </div>
                -->

                <div class="toc-item">
                    <div class="toc-checkbox">
                        <input type="checkbox" id="toc-9" class="form-check-input" checked>
                    </div>
                    <div class="toc-content">
                        <a href="#conclusion_1">
                        <label  class="toc-label">
                            <i class="fas fa-clipboard-check text-success me-2"></i>
                            <strong>Fazit und Zusammenfassung</strong><span class="p-index">Seite 10</span>
                        </label>
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="page-footer"><?php echo $pageIndexData ?></div>
</div>

<style>
.table-of-contents {
    background: #FFF;
    border-radius: 10px;
    padding: 2rem;
    margin: 2rem 0;
}

.table-of-contents h2 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.toc-container {
    margin-top: 1.5rem;
}

.toc-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #0C665A;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.toc-item .form-check-input:checked {
    background-color: #0C665A;
    border-color: var(--tblr-border-color-translucent);
}

.toc-item .p-index {
    float: right;
    display: block;
}

.toc-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.toc-checkbox {
    margin-right: 1rem;
    margin-top: 0.25rem;
}

.toc-checkbox input[type="checkbox"] {
    transform: scale(1.2);
}

.toc-content {
    flex: 1;
}

.toc-label {
    margin: 0;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    width:100%;
}

.toc-label:hover {
    color: #007bff;
}

.toc-label i {
    width: 20px;
    text-align: center;
}

.text-purple {
    color: #6f42c1 !important;
}

.toc-footer .alert {
    border: none;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

@media print {
    .toc-item {
        break-inside: avoid;
        margin-bottom: 0.5rem;
    }

    .table-of-contents {
        background: white !important;
        box-shadow: none !important;
    }
}

@media (max-width: 768px) {
    .table-of-contents {
        padding: 1rem;
        margin: 1rem 0;
    }

    .toc-item {
        padding: 0.5rem;
    }

    .toc-label {
        font-size: 0.9rem;
    }
}
</style>
